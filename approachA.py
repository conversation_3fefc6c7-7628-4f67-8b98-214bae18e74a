import os
import json
import pandas as pd
import requests

# === Configuration ===
FOLDER_PATH = '/Users/<USER>/Downloads/Ebebek Campaign Statistics'  # Replace with the local path to your JSON files
API_URL = "http://sirius.api.staging.internal.minerva/text/v1/generate"
API_KEY = "minerva-test"
HEADERS = {
    "x-api-key": "minerva-test",
    "User-Agent": "insider/dev",
    "Content-Type": "application/json"
}

# === Function to extract flattened variation-level data from JSON ===
def extract_campaign_data(data):
    rows = []
    for campaign in data.get("campaigns", {}).get("details", []):
        nested_variations = campaign.get("nested", [])
        for var in nested_variations:
            var['campaignName'] = campaign.get('campaignName')
            var['builderId'] = campaign.get('builderId')
            var['pageType'] = campaign.get('pageType')
            var['platform'] = campaign.get('platform')
            var['status'] = campaign.get('status')
            var['locale'] = campaign.get('locale')
            rows.append(var)
    return pd.json_normalize(rows)

# === Load JSON files and extract data into DataFrames ===
def load_all_campaigns(folder_path):
    dfs = {}
    for filename in os.listdir(folder_path):
        if filename.lower().endswith('.json'):
            path = os.path.join(folder_path, filename)
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            df = extract_campaign_data(data)
            dfs[filename] = df
    return dfs

# === Prompt generation function ===
def generate_prompt_from_df(df):
    lines = ["You are an expert marketing analyst. Given the following campaign data:\n"]
    for _, row in df.iterrows():
        lines.append(f"Algorithm: {row.get('algorithm', 'N/A')}")
        lines.append(f"Conversion Rate: {row.get('conversionRate', 'N/A')}")
        lines.append(f"Incremental Revenue: {row.get('incrementalRevenueFormatted', 'N/A')}")
        lines.append(f"Impressions: {row.get('impression', 'N/A')}\n")
    lines.append("First, rank the algorithms by conversion rate and incremental revenue.")
    lines.append("Then, output the list of algorithms (at least 20) in JSON array format.")
    lines.append("If the data is insufficient, add fallback algorithms ['Most Popular', 'New Arrivals'].")
    lines.append("Finally, explain your reasoning.")
    return "\n".join(lines)

# === Call Minerva API with exact request structure ===
def call_minerva_api(system_prompt, user_prompt):
    payload = {
        "system_prompt": system_prompt,
        "user_prompt": user_prompt,
        "num_variations": 1,
        "temperature": 0.4,
        "max_tokens": 2048,
        "model": "openai/gpt-3.5-turbo",
        "json_mode": False
    }
    response = requests.post(API_URL, headers=HEADERS, json=payload)
    response.raise_for_status()
    return response.json()


# === Main logic ===
def main():
    dfs = load_all_campaigns(FOLDER_PATH)

    for fname, df in dfs.items():
        print(f"\n=== {fname} ===")
        print("Columns:", df.columns.tolist())
        print(df.head())

        user_prompt = generate_prompt_from_df(df)
        system_prompt = "You are an expert marketing analyst."
        print("\nCalling Minerva API...")

        try:
            response = call_minerva_api(system_prompt, user_prompt)
            print("\nResponse from Minerva:")
            if 'data' in response and len(response['data']) > 0:
                print(response['data'][0])
            else:
                print("Unexpected response format:")
                print(json.dumps(response, indent=2))
        except Exception as e:
            print(f"Failed to get response for {fname}: {e}")

if __name__ == "__main__":
    main()
