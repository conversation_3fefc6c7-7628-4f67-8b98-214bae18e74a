import os
import json
import pandas as pd
import requests

# === Configuration ===
FOLDER_PATH = '/Users/<USER>/Downloads/Ebebek Campaign Statistics'  # <-- Change this to your local path
API_URL = "http://sirius.api.staging.internal.minerva/text/v1/generate"
HEADERS = {
    "x-api-key": "minerva-test",
    "User-Agent": "insider/dev",
    "Content-Type": "application/json"
}

# === Extract variation-level data from campaign JSON ===
def extract_campaign_data(data):
    rows = []
    for campaign in data.get("campaigns", {}).get("details", []):
        for var in campaign.get("nested", []):
            var['campaignName'] = campaign.get('campaignName')
            var['builderId'] = campaign.get('builderId')
            var['pageType'] = campaign.get('pageType')
            var['platform'] = campaign.get('platform')
            var['status'] = campaign.get('status')
            var['locale'] = campaign.get('locale')
            rows.append(var)
    return pd.json_normalize(rows)

# === Load JSON files into DataFrames ===
def load_all_campaigns(folder_path):
    dfs = {}
    for filename in os.listdir(folder_path):
        if filename.lower().endswith('.json'):
            path = os.path.join(folder_path, filename)
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            df = extract_campaign_data(data)
            dfs[filename] = df
    return dfs

# === Step-by-step prompt generator ===
def prepare_prompt(df):
    df = df.copy()

    def parse_percent(p):
        try:
            return float(p.strip('%')) / 100
        except:
            return 0.0

    def parse_currency(c):
        try:
            return float(str(c).replace(",", "").replace("TRY", "").strip())
        except:
            return 0.0

    df['parsed_conversion_rate'] = df['conversionRate'].apply(parse_percent)
    df['parsed_incremental_revenue'] = df['incrementalRevenueFormatted'].apply(parse_currency)
    df['parsed_impressions'] = pd.to_numeric(df['impression'], errors='coerce').fillna(0).astype(int)

    df['success_score'] = (df['parsed_conversion_rate'] * 100) + (df['parsed_incremental_revenue'] / 1000)
    df_sorted = df.sort_values(by='success_score', ascending=False)

    high_imp_count = (df_sorted['parsed_impressions'] > 10000).sum()
    top_algorithms = df_sorted['algorithm'].dropna().unique().tolist()[:20]
    if high_imp_count < 5:
        top_algorithms += ['Most Popular', 'New Arrivals']

    reasoning = (
        "You are a senior marketing analyst. Please follow these steps:\n"
        "1. For each algorithm, calculate a success score as (Conversion Rate * 100) + (Incremental Revenue / 1000).\n"
        "2. Rank all algorithms by success score descending.\n"
        "3. If fewer than 5 algorithms have impressions >10,000, add ['Most Popular','New Arrivals'] to the list.\n"
        "4. Output:\n"
        "- JSON array of top 20 algorithms\n"
        "- Short reasoning explaining ranking criteria.\n\n"
        f"Input:\n\n"
    )

    for _, row in df_sorted.iterrows():
        reasoning += f"Algorithm: {row.get('algorithm', 'N/A')}\n"
        reasoning += f"Conversion Rate: {row.get('conversionRate', 'N/A')}\n"
        reasoning += f"Incremental Revenue: {row.get('incrementalRevenueFormatted', 'N/A')}\n"
        reasoning += f"Impressions: {row.get('impression', 'N/A')}\n\n"

    return reasoning

# === API caller ===
def call_minerva(system_prompt, user_prompt):
    payload = {
        "system_prompt": system_prompt,
        "user_prompt": user_prompt,
        "num_variations": 1,
        "temperature": 0.4,
        "max_tokens": 512,
        "model": "openai/gpt-3.5-turbo",
        "json_mode": False
    }
    response = requests.post(API_URL, headers=HEADERS, json=payload)
    response.raise_for_status()
    return response.json()

# === Main logic ===
def main():
    dfs = load_all_campaigns(FOLDER_PATH)

    for fname, df in dfs.items():
        print(f"\n📄 File: {fname}\n{'='*60}")
        try:
            prompt = prepare_prompt(df)
            result = call_minerva(
                system_prompt="You are a senior marketing analyst.",
                user_prompt=prompt
            )
            for i, variation in enumerate(result.get("output", [])):
                print(f"\nVariation {i+1}:\n{variation}")
        except Exception as e:
            print(f"❌ Error processing {fname}: {e}")

if __name__ == "__main__":
    main()
