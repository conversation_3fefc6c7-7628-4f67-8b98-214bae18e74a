aiohappyeyeballs==2.4.0
aiohttp==3.10.5
aiosignal==1.3.1
annotated-types==0.7.0
anyio==4.4.0
async-timeout==4.0.3
attrs==24.2.0
audioread==3.0.1
cachetools==5.5.0
certifi==2024.8.30
cffi==1.17.0
charset-normalizer==3.3.2
click==8.1.7
cloudpickle==3.0.0
compressed-tensors==0.8.0
contourpy==1.3.0
cycler==0.12.1
datasets==2.21.0
decorator==5.1.1
dill==0.3.8
diskcache==5.6.3
distro==1.9.0
docker-pycreds==0.4.0
docstring_parser==0.16
einops==0.8.0
exceptiongroup==1.2.2
fastapi==0.112.2
filelock==3.15.4
fonttools==4.53.1
frozenlist==1.4.1
fsspec==2024.6.1
gguf==0.10.0
gitdb==4.0.11
GitPython==3.1.43
google-ai-generativelanguage==0.6.6
google-api-core==2.19.2
google-api-python-client==2.143.0
google-auth==2.34.0
google-auth-httplib2==0.2.0
google-cloud-aiplatform==1.64.0
google-cloud-bigquery==3.25.0
google-cloud-core==2.4.1
google-cloud-resource-manager==1.12.5
google-cloud-storage==2.18.2
google-crc32c==1.6.0
google-generativeai==0.7.2
google-resumable-media==2.7.2
googleapis-common-protos==1.65.0
grpc-google-iam-v1==0.13.1
grpcio==1.66.1
grpcio-status==1.62.3
h11==0.14.0
httpcore==1.0.5
httplib2==0.22.0
httptools==0.6.1
httpx==0.27.2
huggingface-hub==0.24.6
idna==3.8
importlib_metadata==8.4.0
interegular==0.3.3
Jinja2==3.1.4
jiter==0.5.0
joblib==1.4.2
jsonschema==4.23.0
jsonschema-specifications==2023.12.1
kiwisolver==1.4.7
lark==1.2.2
lazy_loader==0.4
librosa==0.10.2.post1
llvmlite==0.43.0
lm-format-enforcer==0.10.9
MarkupSafe==2.1.5
matplotlib==3.9.2
mistral_common==1.5.0
mpmath==1.3.0
msgpack==1.0.8
msgspec==0.18.6
multidict==6.0.5
multiprocess==0.70.16
nest-asyncio==1.6.0
networkx==3.3
numba==0.60.0
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==*********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==**********
nvidia-cusparse-cu12==**********
nvidia-ml-py==12.560.30
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.1.105
openai==1.54.5
opencv-python-headless==*********
outlines==0.0.46
packaging==24.1
pandas==2.2.2
partial-json-parser==*******.post4
pillow==10.4.0
platformdirs==4.2.2
pooch==1.8.2
prometheus-fastapi-instrumentator==7.0.0
prometheus_client==0.20.0
proto-plus==1.24.0
protobuf==4.25.4
psutil==6.0.0
py-cpuinfo==9.0.0
pyairports==2.1.1
pyarrow==17.0.0
pyasn1==0.6.0
pyasn1_modules==0.4.0
pycountry==24.6.1
pycparser==2.22
pydantic==2.9.2
pydantic_core==2.23.4
pyparsing==3.1.4
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
pytz==2024.1
PyYAML==6.0.2
pyzmq==26.2.0
ray==2.35.0
referencing==0.35.1
regex==2024.7.24
requests==2.32.3
rpds-py==0.20.0
rsa==4.9
safetensors==0.4.4
scikit-learn==1.5.1
scipy==1.14.1
seaborn==0.13.2
sentencepiece==0.2.0
sentry-sdk==2.13.0
setproctitle==1.3.3
shapely==2.0.6
six==1.16.0
smmap==5.0.1
sniffio==1.3.1
soundfile==0.12.1
soxr==0.5.0.post1
starlette==0.38.4
sympy==1.13.1
threadpoolctl==3.5.0
tiktoken==0.7.0
tokenizers==0.20.3
torch==2.5.1+cu121
torchaudio==2.5.1+cu121
torchvision==0.20.1
tqdm==4.66.5
transformers==4.46.3
triton==3.1.0
typing_extensions==4.12.2
tzdata==2024.1
uritemplate==4.1.1
urllib3==2.2.2
uvicorn==0.30.6
uvloop==0.20.0
vertexai==1.64.0
vllm==0.6.4.post1
wandb==0.17.8
watchfiles==0.24.0
websockets==13.0.1
xformers==0.0.28.post3
xxhash==3.5.0
yarl==1.9.7
zipp==3.20.1
