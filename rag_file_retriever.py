"""
RAG File Retriever
==================
This module selects the **most relevant historical campaign files** to feed into a
Retrieval‑Augmented Generation (RAG) pipeline.

It provides three complementary retrieval strategies:

1. **Seasonality** – *Last‑year neighbours*
   • Top‑``N`` files whose dates are closest to *today − 1 year*.
2. **Recency** – *Latest files this year*
   • Last ``N`` files **before** the current moment.
3. **Metric similarity** – *Behavioural twins*
   • Top‑``N`` files whose **overall behavioural fingerprint** (conversion‑rate, click‑rate, add‑to‑cart‑rate, incremental revenue, impressions) most closely matches the current file.

The union (deduplicated, original order preserved) forms the context sent to the
LLM so that it can reason over patterns from similar temporal windows **and**
behavioural profiles.
"""

from __future__ import annotations
import json
import os
import statistics as _stats
from dataclasses import dataclass
from datetime import datetime
from typing import List, Dict, Any


# Helpers
MONTHS = {
    month.lower(): idx
    for idx, month in enumerate(
        [
            "January",
            "February",
            "March",
            "April",
            "May",
            "June",
            "July",
            "August",
            "September",
            "October",
            "November",
            "December",
        ],
        start=1,
    )
}


def _parse_percent(val: str | float | int) -> float:
    """"0.08%" ➜ 0.0008"""
    try:
        if isinstance(val, (int, float)):
            return float(val)
        return float(str(val).replace("%", "").strip()) / 100.0
    except Exception:
        return 0.0


def _parse_currency(val: str | float | int) -> float:
    """"19,859.07 TRY" ➜ 19859.07"""
    try:
        if isinstance(val, (int, float)):
            return float(val)
        return float(str(val).upper().replace("TRY", "").replace(",", "").strip())
    except Exception:
        return 0.0


def _parse_date_from_filename(fname: str, *, default_year: int | None = None) -> datetime:
    """Infer date from filename fragments like ``February_1_2025.json`` or ``2024_February_1``."""
    default_year = default_year or datetime.now().year

    name = os.path.splitext(os.path.basename(fname))[0]
    parts = name.split("_")

    month = None
    day = 1
    year = default_year

    # find month name token
    for i, token in enumerate(parts):
        token_low = token.lower()
        if token_low in MONTHS:
            month = MONTHS[token_low]
            # day may follow
            if i + 1 < len(parts):
                try:
                    day = int(parts[i + 1])
                except ValueError:
                    pass
            break

    # look for 4‑digit year anywhere
    for token in parts:
        if token.isdigit() and len(token) == 4:
            year = int(token)
            break

    month = month or 1
    try:
        return datetime(year, month, day)
    except Exception:
        return datetime(default_year, 1, 1)


# Core data structure
@dataclass
class FileInfo:
    path: str
    date: datetime

    conversion_rate: float
    click_rate: float
    add_to_cart_rate: float
    incremental_revenue: float
    impressions: int

    raw_data: Dict[str, Any]


# Main retrieval class
class FileRetriever:
    """Scan a folder and retrieve the most relevant campaign files for RAG."""

    # Init / file loading
    def __init__(self, folder_path: str):
        if not os.path.isdir(folder_path):
            raise ValueError(f"{folder_path!r} is not a directory")
        self.folder_path = folder_path
        self.files: List[FileInfo] = self._load_files()
        if not self.files:
            raise RuntimeError("No JSON files found – check folder path and naming.")

        # Pre‑compute mean/std for z‑score normalisation
        self._metric_stats: Dict[str, tuple[float, float]] = {
            "conversion_rate": (
                _stats.mean(fi.conversion_rate for fi in self.files),
                _stats.pstdev(fi.conversion_rate for fi in self.files) or 1.0,
            ),
            "click_rate": (
                _stats.mean(fi.click_rate for fi in self.files),
                _stats.pstdev(fi.click_rate for fi in self.files) or 1.0,
            ),
            "add_to_cart_rate": (
                _stats.mean(fi.add_to_cart_rate for fi in self.files),
                _stats.pstdev(fi.add_to_cart_rate for fi in self.files) or 1.0,
            ),
            "incremental_revenue": (
                _stats.mean(fi.incremental_revenue for fi in self.files),
                _stats.pstdev(fi.incremental_revenue for fi in self.files) or 1.0,
            ),
            "impressions": (
                _stats.mean(fi.impressions for fi in self.files),
                _stats.pstdev(fi.impressions for fi in self.files) or 1.0,
            ),
        }

    def _load_files(self) -> List[FileInfo]:
        infos: List[FileInfo] = []
        for fname in os.listdir(self.folder_path):
            if not fname.lower().endswith(".json"):
                continue
            path = os.path.join(self.folder_path, fname)
            try:
                with open(path, "r", encoding="utf-8") as fp:
                    data = json.load(fp)

                date = _parse_date_from_filename(fname, default_year=datetime.now().year)
                details = data.get("campaigns", {}).get("details", [])
                summary = data.get("summary", {})

                get = lambda key, default="0%": summary.get(key) or (details[0].get(key) if details else default)

                cr = _parse_percent(get("conversionRate", "0%"))
                clk = _parse_percent(get("clickRate", "0%"))
                atc = _parse_percent(get("addToCartRate", "0%"))
                rev = _parse_currency(get("incrementalRevenueFormatted", 0))
                impr = int(details[0].get("impression", 0)) if details else 0

                infos.append(
                    FileInfo(
                        path=path,
                        date=date,
                        conversion_rate=cr,
                        click_rate=clk,
                        add_to_cart_rate=atc,
                        incremental_revenue=rev,
                        impressions=impr,
                        raw_data=data,
                    )
                )
            except Exception as exc:
                print(f"[FileRetriever] Skipping {fname}: {exc}")
        return infos

    # Public API
    def retrieve_context_files(
        self,
        *,
        current_file_path: str,
        last_year_n: int = 5,
        recent_n: int = 5,
        similar_n: int = 5,
        dedup: bool = True,
    ) -> List[str]:
        current = self._find_by_path(current_file_path)
        if current is None:
            raise ValueError(f"Current file {current_file_path!r} not found in {self.folder_path!r}.")

        today = datetime.now()
        last_year_files = self._closest_dates_last_year(today, n=last_year_n)
        recent_files = self._most_recent_before(today, n=recent_n)
        metric_similar_files = self._closest_by_metrics(current, n=similar_n)

        ordered: List[FileInfo] = last_year_files + recent_files + metric_similar_files

        if dedup:
            seen = {current.path}
            result: List[str] = []
            for fi in ordered:
                if fi.path not in seen:
                    result.append(fi.path)
                    seen.add(fi.path)
            return result
        else:
            return [fi.path for fi in ordered if fi.path != current.path]

   
    # Selection strategies
    def _closest_dates_last_year(self, today: datetime, *, n: int) -> List[FileInfo]:
        target = today.replace(year=today.year - 1)
        return sorted(self.files, key=lambda fi: abs((fi.date - target).days))[:n]

    def _most_recent_before(self, today: datetime, *, n: int) -> List[FileInfo]:
        past = [fi for fi in self.files if fi.date < today]
        past.sort(key=lambda fi: fi.date, reverse=True)
        return past[:n]

    # Multi‑metric similarity
    def _z(self, value: float, metric: str) -> float:
        mean, std = self._metric_stats[metric]
        return (value - mean) / std if std else 0.0

    def _vector(self, fi: FileInfo) -> List[float]:
        return [
            self._z(fi.conversion_rate, "conversion_rate"),
            self._z(fi.click_rate, "click_rate"),
            self._z(fi.add_to_cart_rate, "add_to_cart_rate"),
            self._z(fi.incremental_revenue, "incremental_revenue"),
            self._z(fi.impressions, "impressions"),
        ]

    def _closest_by_metrics(self, pivot: FileInfo, *, n: int) -> List[FileInfo]:
        pv = self._vector(pivot)

        def dist(fi: FileInfo) -> float:
            return sum((a - b) ** 2 for a, b in zip(pv, self._vector(fi))) ** 0.5

        ranked = sorted(self.files, key=dist)
        return [fi for fi in ranked if fi.path != pivot.path][:n]

 
# Utility
    def _find_by_path(self, path: str) -> FileInfo | None:
        abs_path = os.path.abspath(path)
        for fi in self.files:
            if os.path.abspath(fi.path) == abs_path:
                return fi
        return None


# CLI entry‑point
if __name__ == "__main__":
    import argparse
    import textwrap

    parser = argparse.ArgumentParser(
        description=textwrap.dedent(
            """Select historical campaign files for RAG and print selection (one per line)."""
        )
    )


    parser.add_argument("folder", help="Folder containing JSON files")
    parser.add_argument("current", help="Path of the *current* JSON file")
    parser.add_argument("--last-year-n", type=int, default=5, help="Last‑year neighbours [default: 5]")
    parser.add_argument("--recent-n", type=int, default=5, help="Recent‑year files [default: 5]")
    parser.add_argument("--similar-n", type=int, default=5, help="Metric‑similar files [default: 5]")

    args = parser.parse_args()
    
    folder_path = "/Users/<USER>/Downloads/Ebebek Campaign Statistics"
    current_path = "/Users/<USER>/Downloads/Ebebek Campaign Statistics/ebebek_campaign_February_1.json"
    retriever = FileRetriever(folder_path=folder_path)
    selection = retriever.retrieve_context_files(
        current_file_path=current_path,
        last_year_n=5,
        recent_n=5,
        similar_n=5,
    )
    print("\n".join(selection))
