import os
import json
import pandas as pd
import requests

# === Configuration ===
FOLDER_PATH = '/Users/<USER>/Downloads/Ebebek Campaign Statistics'  # <-- Update this with your local path
API_URL = "http://sirius.api.staging.internal.minerva/text/v1/generate"
HEADERS = {
    "x-api-key": "minerva-test",
    "User-Agent": "insider/dev",
    "Content-Type": "application/json"
}

# === Extract variation-level data from JSON ===
def extract_campaign_data(data):
    rows = []
    for campaign in data.get("campaigns", {}).get("details", []):
        for var in campaign.get("nested", []):
            var['campaignName'] = campaign.get('campaignName')
            var['builderId'] = campaign.get('builderId')
            var['pageType'] = campaign.get('pageType')
            var['platform'] = campaign.get('platform')
            var['status'] = campaign.get('status')
            var['locale'] = campaign.get('locale')
            rows.append(var)
    return pd.json_normalize(rows)

# === Load all JSON files ===
def load_all_campaigns(folder_path):
    dfs = {}
    for filename in os.listdir(folder_path):
        if filename.lower().endswith('.json'):
            path = os.path.join(folder_path, filename)
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            df = extract_campaign_data(data)
            dfs[filename] = df
    return dfs

# === Create Markdown Table Prompt ===
def create_markdown_table(df):
    df = df.copy()

    def parse_percent(p):
        try:
            return float(str(p).replace("%", "").strip()) / 100
        except:
            return 0.0

    def parse_currency(c):
        try:
            return float(str(c).replace(",", "").replace("TRY", "").strip())
        except:
            return 0.0

    df['ConversionRate'] = df['conversionRate'].apply(parse_percent)
    df['IncrementalRevenue'] = df['incrementalRevenueFormatted'].apply(parse_currency)
    df['Impressions'] = pd.to_numeric(df['impression'], errors='coerce').fillna(0).astype(int)
    df['Algorithm'] = df['algorithm'].astype(str)

    table_lines = [
        "Algorithm | ConversionRate | IncrementalRevenue | Impressions",
        "--- | --- | --- | ---"
    ]
    for _, row in df.iterrows():
        table_lines.append(
            f"{row['Algorithm']} | {row['ConversionRate']:.2f} | {row['IncrementalRevenue']:.2f} | {row['Impressions']}"
        )
    return "\n".join(table_lines)

# === Build Prompt from Table ===
def build_prompt_from_table(df):
    table_text = create_markdown_table(df)
    instructions = (
        "You are a data analyst. Here is a table:\n\n"
        f"{table_text}\n\n"
        "Instructions:\n"
        "1. Rank algorithms by ConversionRate and IncrementalRevenue.\n"
        "2. If data is insufficient, append ['Most Popular','New Arrivals'].\n"
        "3. Return a JSON array of top algorithms and a short explanation."
    )
    return instructions

# === API Caller ===
def call_minerva(system_prompt, user_prompt):
    payload = {
        "system_prompt": system_prompt,
        "user_prompt": user_prompt,
        "num_variations": 1,
        "temperature": 0.4,
        "max_tokens": 2048,
        "model": "openai/gpt-3.5-turbo",
        "json_mode": False
    }
    response = requests.post(API_URL, headers=HEADERS, json=payload)
    response.raise_for_status()
    return response.json()

# === Main Execution ===
def main():
    dfs = load_all_campaigns(FOLDER_PATH)

    for fname, df in dfs.items():
        print(f"\n📊 File: {fname}\n{'='*60}")
        try:
            prompt = build_prompt_from_table(df)
            result = call_minerva(
                system_prompt="You are a data analyst.",
                user_prompt=prompt
            )
            for i, variation in enumerate(result.get("data", [])):
                print(f"\nVariation {i+1}:\n{variation}")
        except Exception as e:
            print(f"❌ Error processing {fname}: {e}")

if __name__ == "__main__":
    main()
