import os
import json
import pandas as pd
import requests
from datetime import datetime
import subprocess
import sys
import re

# Configuration
FOLDER_PATH = '/Users/<USER>/Downloads/Ebebek Campaign Statistics'
API_URL = "http://sirius.api.staging.internal.minerva/text/v1/generate"

HEADERS = {
    "x-api-key": "minerva-test",
    "User-Agent": "insider/dev",
    "Content-Type": "application/json"
}

def parse_date_from_filename(filename):
    """Parse date from filename ending with format like 'February_1'"""
    try:
        if '_' in filename:
            parts = filename.split('_')
            if len(parts) >= 2:
                month_name = parts[-2]
                day = parts[-1].split('.')[0]
                date_str = f"{month_name} {day} 2025"
                return datetime.strptime(date_str, "%B %d %Y")
    except:
        pass
    return datetime(2025, 1, 1)

def sort_filenames_by_date(filenames):
    """Sort filenames by their embedded dates (earliest first)"""
    return sorted(filenames, key=parse_date_from_filename)

def validate_algorithm_count(llm_response):
    """Validate that the LLM response contains exactly 16 algorithms in the ranking."""
    try:
        if "RANKING:" in llm_response:
            ranking_section = llm_response.split("RANKING:")[1]
            numbered_lines = re.findall(r'^\d+\.\s+', ranking_section, re.MULTILINE)
            algorithm_count = len(numbered_lines)
            return algorithm_count == 16, algorithm_count
        else:
            return False, 0
    except:
        return False, 0

def get_rag_files(current_file_path):
    """Get relevant files using RAG retrieval"""
    try:
        # Run the RAG file retriever
        result = subprocess.run([
            'python3', '/Users/<USER>/Downloads/rag_file_retriever.py',
            FOLDER_PATH,
            current_file_path,
            '--recent-n', '5',
            '--similar-n', '3'
        ], capture_output=True, text=True, cwd='/Users/<USER>/Downloads')
        
        if result.returncode == 0:
            # Parse the output to get file paths
            file_paths = []
            for line in result.stdout.strip().split('\n'):
                if line.strip() and line.startswith('/'):
                    file_paths.append(line.strip())
            return file_paths
        else:
            print(f"RAG retrieval failed: {result.stderr}")
            return []
    except Exception as e:
        print(f"Error running RAG retrieval: {e}")
        return []

# Extract variation-level data from JSON 
def extract_campaign_data(data):
    rows = []
    for campaign in data.get("campaigns", {}).get("details", []):
        for var in campaign.get("nested", []):
            var['campaignName'] = campaign.get('campaignName')
            var['builderId'] = campaign.get('builderId')
            var['pageType'] = campaign.get('pageType')
            var['platform'] = campaign.get('platform')
            var['status'] = campaign.get('status')
            var['locale'] = campaign.get('locale')
            rows.append(var)
    return pd.json_normalize(rows)

# Load specific JSON files
def load_specific_campaigns(file_paths):
    dfs = {}
    for file_path in file_paths:
        try:
            filename = os.path.basename(file_path)
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            df = extract_campaign_data(data)
            dfs[filename] = df
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
    return dfs

# RAG Algorithm Ranker with ICRL-style prompting
class RAGAlgorithmRanker:
    def __init__(self):
        self.context_examples = []
        self.max_context_examples = 8

    def parse_campaign_data(self, df):
        df = df.copy()

        def parse_percent(p):
            try:
                return float(str(p).replace("%", "").strip()) / 100
            except:
                return 0.0

        def parse_currency(c):
            try:
                return float(str(c).replace(",", "").replace("TRY", "").strip())
            except:
                return 0.0

        df['parsed_conversion_rate'] = df['conversionRate'].apply(parse_percent)
        df['parsed_incremental_revenue'] = df['incrementalRevenueFormatted'].apply(parse_currency)
        df['parsed_impressions'] = pd.to_numeric(df['impression'], errors='coerce').fillna(0).astype(int)

        # New features
        df['parsed_click_rate'] = df.get('clickRate', 0).apply(parse_percent) if 'clickRate' in df else 0.0
        df['parsed_add_to_cart_rate'] = df.get('addToCartRate', 0).apply(parse_percent) if 'addToCartRate' in df else 0.0

        df['success_score'] = (df['parsed_conversion_rate'] * 100) + (df['parsed_incremental_revenue'] / 1000)
        df['engagement_score'] = (df['parsed_click_rate'] * 100) + (df['parsed_add_to_cart_rate'] * 100)

        # Combine scores with adjusted weights
        alpha = 0.7  # weight for success_score
        beta = 0.3   # weight for engagement_score
        df['final_score'] = (alpha * df['success_score']) + (beta * df['engagement_score'])

        return df

    def build_system_prompt(self):
        """Build system prompt following ICRL strategy"""
        return """You are an expert algorithm analyst. Your task is to identify and rank the top 16 algorithms that will achieve the best performance scores. The feedback you receive will be the actual performance scores achieved by the algorithms you predict. You must learn from these numerical feedback values to make better algorithm predictions.

Task: Find and rank exactly 16 algorithms that will have the best performance scores.

Available Algorithm Types:
- user_based - Recommendations based on user behavior and preferences
- view_to_view - Recommendations based on "users who viewed this also viewed that"
- buy_to_buy - Recommendations based on "users who bought this also bought that"
- most_viewed_of_partner - Most viewed products across the partner's catalog
- most_viewed_of_category - Most viewed products within a specific category
- most_purchased_of_partner - Most purchased products across the partner's catalog
- most_purchased_of_category - Most purchased products within a specific category
- most_purchased_of_location - Most purchased products in a specific location
- trending_products_of_partner - Products trending across the partner's catalog
- highest_discounted_of_partner - Products with highest discounts across the partner's catalog
- new_arrivals_of_partner - Newly added products across the partner's catalog
- complementary - Products that complement other products (often purchased together)
- substitute - Products that can substitute for other products

Enhanced Scoring System:
- Success Score = (Conversion Rate * 100) + (Incremental Revenue / 1000)
- Engagement Score = (Click Rate * 100) + (Add to Cart Rate * 100)  
- Final Score = (0.7 * Success Score) + (0.3 * Engagement Score)

The feedback will show you the actual final scores that your predicted algorithms achieved. Use this numerical feedback to learn which algorithms perform best overall, considering both conversion/revenue performance and user engagement patterns. You must always provide exactly 16 algorithms in your ranking."""

    def build_context_from_history(self):
        if not self.context_examples:
            return ""

        context = "\nPREVIOUS ALGORITHM PERFORMANCE FEEDBACK (CHRONOLOGICAL ORDER)\n" + "=" * 60 + "\n"

        for example in self.context_examples[-self.max_context_examples:]:
            campaign_date = example['campaign_date'].strftime('%B %d')
            context += f"\nDATE: {campaign_date} ({example['campaign_name']})\n"
            
            # Show ALL algorithms from this dataset
            all_algorithms = sorted(example['variations'], key=lambda x: x.get('final_score', x['success_score']), reverse=True)
            print(f"Found {len(all_algorithms)} algorithms in context example: {example['campaign_name']}")
            
            for var in all_algorithms:
                context += f"Algorithm: {var['algorithm']}\n"
                context += f"Performance Score: {var.get('final_score', var['success_score']):.2f}\n"
                context += f"Success Score: {var['success_score']:.2f}, Engagement Score: {var.get('engagement_score', 0):.2f}\n"
                context += f"Metrics: CR={var['conversionRate']}, Revenue={var['incrementalRevenue']}, Impressions={var['impression']}\n\n"
            
            context += "-" * 40 + "\n"

        return context + "Learn from these algorithm performance scores to rank algorithms by their expected performance.\n"

    def build_algorithm_ranking_prompt(self):
        """Build ICRL-style prompt that asks for top 16 algorithm ranking with reasoning."""
        context_history = self.build_context_from_history()

        prompt = f"""{context_history}
CURRENT TASK:
Rank the top 16 algorithms that will achieve the highest performance scores.

Available Algorithms:
- user_based - Recommendations based on user behavior and preferences
- view_to_view - Recommendations based on "users who viewed this also viewed that"
- buy_to_buy - Recommendations based on "users who bought this also bought that"
- most_viewed_of_partner - Most viewed products across the partner's catalog
- most_viewed_of_category - Most viewed products within a specific category
- most_purchased_of_partner - Most purchased products across the partner's catalog
- most_purchased_of_category - Most purchased products within a specific category
- most_purchased_of_location - Most purchased products in a specific location
- trending_products_of_partner - Products trending across the partner's catalog
- highest_discounted_of_partner - Products with highest discounts across the partner's catalog
- new_arrivals_of_partner - Newly added products across the partner's catalog
- complementary - Products that complement other products (often purchased together)
- substitute - Products that can substitute for other products

Enhanced Scoring System:
- Success Score = (Conversion Rate * 100) + (Incremental Revenue / 1000)
- Engagement Score = (Click Rate * 100) + (Add to Cart Rate * 100)
- Final Score = (0.7 * Success Score) + (0.3 * Engagement Score)

Based on the previous feedback showing actual final performance scores achieved by different algorithms, provide:

1. REASONING: Explain your analysis of which algorithms perform best based on the performance patterns you've learned from the feedback data.

2. TOP 16 ALGORITHM RANKING: List exactly 16 algorithms in order of predicted performance (highest to lowest performance score).

Format your response as:
REASONING: [Your detailed reasoning here]

RANKING:
1. [Algorithm Name]
2. [Algorithm Name]
...
16. [Algorithm Name]"""

        return prompt

    def add_campaign_example(self, campaign_name, df):
        """Add campaign example to context, keeping only the latest 8 examples."""
        variations = []
        for _, row in df.iterrows():
            variations.append({
                "variationId": row.get("variationId"),
                "algorithm": row.get("algorithm"),
                "platform": row.get("platform"),
                "pageType": row.get("pageType"),
                "conversionRate": row.get("conversionRate"),
                "incrementalRevenue": row.get("incrementalRevenueFormatted"),
                "impression": row.get("impression"),
                "success_score": row.get("success_score", 0.0),
                "engagement_score": row.get("engagement_score", 0.0),
                "final_score": row.get("final_score", row.get("success_score", 0.0))
            })

        # Add new example with date for sorting
        self.context_examples.append({
            "campaign_name": campaign_name,
            "campaign_date": parse_date_from_filename(campaign_name),
            "variations": variations
        })

        # Sort by date (earliest first) and keep only the latest 8 examples
        self.context_examples.sort(key=lambda x: x['campaign_date'])
        if len(self.context_examples) > self.max_context_examples:
            self.context_examples = self.context_examples[-self.max_context_examples:]

# API caller
def call_minerva_api(system_prompt, user_prompt):
    payload = {
        "system_prompt": system_prompt,
        "user_prompt": user_prompt,
        "num_variations": 1,
        "temperature": 0.4,
        "max_tokens": 2048,
        "model": "openai/gpt-3.5-turbo",
        "json_mode": False
    }
    response = requests.post(API_URL, headers=HEADERS, json=payload)
    response.raise_for_status()
    return response.json()

# Main execution with RAG
def main():
    if len(sys.argv) < 2:
        print("Usage: python3 approachD_RAG.py <current_file_path>")
        print("Example: python3 approachD_RAG.py '/Users/<USER>/Downloads/Ebebek Campaign Statistics/ebebek_campaign_February_1.json'")
        sys.exit(1)

    current_file_path = sys.argv[1]

    print("\nRAG-Enhanced Algorithm Ranking\n" + "=" * 60)

    # Get relevant files using RAG
    print("🔍 Retrieving relevant files using RAG...")
    rag_files = get_rag_files(current_file_path)

    if not rag_files:
        print("❌ No relevant files found via RAG. Exiting.")
        return

    print(f"📁 Found {len(rag_files)} relevant files:")
    for file_path in rag_files:
        filename = os.path.basename(file_path)
        parsed_date = parse_date_from_filename(filename)
        print(f"  - {filename} ({parsed_date.strftime('%B %d')})")
    print()

    # Load the relevant campaigns
    dfs = load_specific_campaigns(rag_files)
    rag_ranker = RAGAlgorithmRanker()

    # Sort filenames by date (earliest to latest)
    sorted_filenames = sort_filenames_by_date(list(dfs.keys()))

    # Process files in chronological order
    for fname in sorted_filenames:
        df = dfs[fname]
        print(f"\n=== {fname} ===")

        try:
            parsed_df = rag_ranker.parse_campaign_data(df)

            # Build prompts for LLM
            system_prompt = rag_ranker.build_system_prompt()
            user_prompt = rag_ranker.build_algorithm_ranking_prompt()

            # Retry logic to ensure 16 algorithms are returned
            max_retries = 5
            retry_count = 0
            valid_response = False

            while retry_count < max_retries and not valid_response:
                # Make API call to get algorithm ranking with reasoning
                result = call_minerva_api(system_prompt, user_prompt)

                if 'data' in result and result['data']:
                    prediction = result['data'][0]

                    # Always print the LLM response for debugging
                    print(f"\n=== LLM RESPONSE (Attempt {retry_count + 1}) ===")
                    print(prediction)
                    print("=" * 50)

                    # Validate algorithm count
                    is_valid, algorithm_count = validate_algorithm_count(prediction)

                    if is_valid:
                        print(f"✓ Validated: Found exactly 16 algorithms in ranking.")

                        # Add campaign data to context for future learning
                        rag_ranker.add_campaign_example(fname, parsed_df)
                        print(f"✓ Added campaign to context. Context now contains {len(rag_ranker.context_examples)} examples.")
                        valid_response = True

                    else:
                        retry_count += 1
                        print(f"⚠️  Attempt {retry_count}: got {algorithm_count} algos, retrying…")

                        if retry_count < max_retries:
                            # Add emphasis to the prompt for retry
                            user_prompt += f"\n\nIMPORTANT: You MUST provide exactly 16 algorithms in your ranking. Previous attempt had {algorithm_count} algorithms."

                else:
                    retry_count += 1
                    print(f"❌ No valid response from LLM API. Retrying... ({retry_count}/{max_retries})")

            if not valid_response:
                print(f"❌  FAILED after {max_retries} tries for {fname}.")

        except Exception as e:
            print(f"❌ Error processing {fname}: {e}")

    print(f"\n🎓 RAG-Enhanced Algorithm Ranking Complete!")
    print(f"Total context examples stored: {len(rag_ranker.context_examples)}")

if __name__ == "__main__":
    main()
