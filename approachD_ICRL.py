import os
import json
import pandas as pd
import requests

# Configuration
FOLDER_PATH = '/Users/<USER>/Downloads/Ebebek Campaign Statistics'
API_URL = "http://sirius.api.staging.internal.minerva/text/v1/generate"
HEADERS = {
    "x-api-key": "minerva-test",
    "User-Agent": "insider/dev",
    "Content-Type": "application/json"
}

# Extract variation-level data from JSON 
def extract_campaign_data(data):
    rows = []
    for campaign in data.get("campaigns", {}).get("details", []):
        for var in campaign.get("nested", []):
            var['campaignName'] = campaign.get('campaignName')
            var['builderId'] = campaign.get('builderId')
            var['pageType'] = campaign.get('pageType')
            var['platform'] = campaign.get('platform')
            var['status'] = campaign.get('status')
            var['locale'] = campaign.get('locale')
            rows.append(var)
    return pd.json_normalize(rows)

# Load all JSON files
def load_all_campaigns(folder_path):
    dfs = {}
    for filename in os.listdir(folder_path):
        if filename.lower().endswith('.json'):
            path = os.path.join(folder_path, filename)
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            df = extract_campaign_data(data)
            dfs[filename] = df
    return dfs

# ICRL (In-Context Reinforcement Learning) Implementation
class ICRLAlgorithmRanker:
    """
    ICRL-based algorithm ranking system that learns from feedback.

    The system makes predictions about algorithm rankings, receives feedback
    on the quality of those predictions, and uses that feedback to improve
    future rankings through in-context learning.
    """

    def __init__(self):
        self.context_examples = []  # Store actual campaign data and rewards
        self.max_context_examples = 10  # Limit context size

    def parse_campaign_data(self, df):
        """Parse and prepare campaign data for analysis."""
        df = df.copy()

        def parse_percent(p):
            try:
                return float(str(p).replace("%", "").strip())
            except:
                return 0.0

        def parse_currency(c):
            try:
                return float(str(c).replace(",", "").replace("TRY", "").strip())
            except:
                return 0.0

        df['parsed_conversion_rate'] = df['conversionRate'].apply(parse_percent)
        df['parsed_incremental_revenue'] = df['incrementalRevenueFormatted'].apply(parse_currency)
        df['parsed_impressions'] = pd.to_numeric(df['impression'], errors='coerce').fillna(0).astype(int)

        return df

    def build_icrl_system_prompt(self):
        """Build the system prompt for ICRL algorithm ranking."""
        return """You are an expert e-commerce algorithm analyst. Your task is to rank recommendation algorithms based on performance data.

You will receive campaign performance data and must predict which algorithms will perform best. Feedback will indicate if your rankings are accurate. Use this feedback to improve your future predictions.

Focus on:
1. Conversion rate effectiveness
2. Revenue generation potential
3. Impression volume and reach
4. Overall business impact

Learn from feedback to make better algorithm recommendations."""

    def build_context_from_history(self):
        """Build context from actual campaign data and calculated rewards."""
        if not self.context_examples:
            return ""

        context = "\nPREVIOUS CAMPAIGN EXAMPLES AND REWARDS\n"
        context += "=" * 50 + "\n"

        for i, example in enumerate(self.context_examples[-self.max_context_examples:]):
            context += f"\nCampaign Example {i+1}:\n"
            context += f"Campaign: {example['campaign_name']}\n"

            # Show top 3 algorithms from this campaign
            top_algorithms = sorted(
                example['algorithm_rewards'].items(),
                key=lambda x: x[1]['reward'],
                reverse=True
            )[:3]

            context += "Top Performing Algorithms:\n"
            for rank, (alg, data) in enumerate(top_algorithms, 1):
                context += f"  {rank}. {alg}\n"
                context += f"     - Reward: {data['reward']:.3f}\n"
                context += f"     - Conversion Rate: {data['raw_conversion_rate']:.3f}%\n"
                context += f"     - Revenue: {data['raw_revenue']:.0f} TRY\n"

            context += f"Total Campaign Reward: {example['total_reward']:.3f}\n"
            context += "-" * 40 + "\n"

        context += "\nUse these actual campaign results to inform your ranking predictions.\n"
        context += "Focus on algorithms that have shown high rewards in similar campaigns.\n"
        context += "=" * 60 + "\n\n"

        return context

    def build_algorithm_ranking_prompt(self, df):
        """Build the main prompt for algorithm ranking using approach B logic with ICRL context."""
        context_history = self.build_context_from_history()

        # Apply approach B logic for success score calculation
        df_work = df.copy()

        def parse_percent(p):
            try:
                return float(str(p).strip('%')) / 100
            except:
                return 0.0

        def parse_currency(c):
            try:
                return float(str(c).replace(",", "").replace("TRY", "").strip())
            except:
                return 0.0

        df_work['parsed_conversion_rate'] = df_work['conversionRate'].apply(parse_percent)
        df_work['parsed_incremental_revenue'] = df_work['incrementalRevenueFormatted'].apply(parse_currency)
        df_work['parsed_impressions'] = pd.to_numeric(df_work['impression'], errors='coerce').fillna(0).astype(int)

        # Calculate success score as in approach B
        df_work['success_score'] = (df_work['parsed_conversion_rate'] * 100) + (df_work['parsed_incremental_revenue'] / 1000)
        df_sorted = df_work.sort_values(by='success_score', ascending=False)

        high_imp_count = (df_sorted['parsed_impressions'] > 10000).sum()
        top_algorithms = df_sorted['algorithm'].dropna().unique().tolist()[:20]
        if high_imp_count < 5:
            top_algorithms += ['Most Popular', 'New Arrivals']

        # Build prompt with ICRL context + approach B instructions
        prompt = f"""{context_history}CURRENT TASK: You are a senior marketing analyst. Please follow these steps:

1. For each algorithm, calculate a success score as (Conversion Rate * 100) + (Incremental Revenue / 1000).
2. Rank all algorithms by success score descending.
3. If fewer than 5 algorithms have impressions >10,000, add ['Most Popular','New Arrivals'] to the list.
4. Output:
   - JSON array of top 20 algorithms
   - Short reasoning explaining ranking criteria.

Input:

"""

        # Add algorithm data as in approach B
        for _, row in df_sorted.iterrows():
            prompt += f"Algorithm: {row.get('algorithm', 'N/A')}\n"
            prompt += f"Conversion Rate: {row.get('conversionRate', 'N/A')}\n"
            prompt += f"Incremental Revenue: {row.get('incrementalRevenueFormatted', 'N/A')}\n"
            prompt += f"Impressions: {row.get('impression', 'N/A')}\n\n"

        return prompt

    def add_campaign_example(self, campaign_name, reward_data):
        """Add actual campaign data and rewards as context for future predictions."""
        campaign_example = {
            'campaign_name': campaign_name,
            'algorithm_rewards': reward_data['algorithm_rewards'],
            'total_reward': reward_data['total_reward'],
            'average_reward': reward_data['average_reward'],
            'max_revenue': reward_data['max_revenue'],
            'max_conversion_rate': reward_data['max_conversion_rate']
        }

        self.context_examples.append(campaign_example)

        # Keep only recent examples to manage context size
        if len(self.context_examples) > self.max_context_examples:
            self.context_examples = self.context_examples[-self.max_context_examples:]

    def calculate_reward(self, df, lambda1=0.6, lambda2=0.4):
        """
        Calculate scalar reward based on normalized revenue and conversion rate.

        Args:
            df: DataFrame with campaign data
            lambda1: Weight for normalized revenue (default 0.6)
            lambda2: Weight for normalized conversion rate (default 0.4)

        Returns:
            dict: Rewards for each algorithm and overall metrics
        """
        # Parse data if not already done
        if 'parsed_conversion_rate' not in df.columns:
            df = self.parse_campaign_data(df)

        # Calculate max values for normalization
        max_revenue = df['parsed_incremental_revenue'].max()
        max_conversion_rate = df['parsed_conversion_rate'].max()

        # Avoid division by zero
        if max_revenue == 0:
            max_revenue = 1
        if max_conversion_rate == 0:
            max_conversion_rate = 1

        # Calculate normalized values and rewards
        rewards = {}
        total_reward = 0

        for _, row in df.iterrows():
            algorithm = row.get('algorithm', 'Unknown')

            # Normalize metrics
            normalized_revenue = row['parsed_incremental_revenue'] / max_revenue
            normalized_conversion_rate = row['parsed_conversion_rate'] / max_conversion_rate

            # Calculate reward
            reward = lambda1 * normalized_revenue + lambda2 * normalized_conversion_rate

            rewards[algorithm] = {
                'reward': reward,
                'normalized_revenue': normalized_revenue,
                'normalized_conversion_rate': normalized_conversion_rate,
                'raw_revenue': row['parsed_incremental_revenue'],
                'raw_conversion_rate': row['parsed_conversion_rate']
            }

            total_reward += reward

        # Calculate average reward
        avg_reward = total_reward / len(rewards) if rewards else 0

        return {
            'algorithm_rewards': rewards,
            'total_reward': total_reward,
            'average_reward': avg_reward,
            'max_revenue': max_revenue,
            'max_conversion_rate': max_conversion_rate
        }

    def evaluate_prediction_quality(self, prediction, reward_data):
        """
        Evaluate prediction quality based on how well it aligns with calculated rewards.

        Args:
            prediction: The model's prediction/ranking
            reward_data: Calculated reward data
            
        Returns:
            float: Scalar feedback score (0-1)
        """
        try:
            # Extract algorithm names from prediction if possible
            prediction_lower = prediction.lower()

            # Get top algorithms by reward
            sorted_algorithms = sorted(
                reward_data['algorithm_rewards'].items(),
                key=lambda x: x[1]['reward'],
                reverse=True
            )

            top_3_algorithms = [alg[0].lower() for alg in sorted_algorithms[:3]]

            # Check if prediction mentions top algorithms early
            score = 0.0
            prediction_words = prediction_lower.split()

            for i, top_alg in enumerate(top_3_algorithms):
                alg_words = top_alg.split()
                for alg_word in alg_words:
                    if alg_word in prediction_words[:100]:  # Check first 100 words
                        # Higher score for algorithms mentioned earlier
                        position_bonus = (3 - i) / 3  # 1.0, 0.67, 0.33
                        score += position_bonus * 0.3
                        break

            # Bonus for structured output
            if "json" in prediction_lower:
                score += 0.2
            if "rank" in prediction_lower:
                score += 0.1
            if len(prediction) > 200:  # Detailed response
                score += 0.1

            return min(score, 1.0)  # Cap at 1.0

        except Exception as e:
            print(f"Error evaluating prediction: {e}")
            return 0.1  # Minimal score for errors

# API Caller 
def call_minerva_icrl(system_prompt, user_prompt):
    payload = {
        "system_prompt": system_prompt,
        "user_prompt": user_prompt,
        "num_variations": 1,
        "temperature": 0.4,  # Higher temperature for more diverse learning
        "max_tokens": 2048,
        "model": "openai/gpt-3.5-turbo",
        "json_mode": False
    }
    response = requests.post(API_URL, headers=HEADERS, json=payload)
    response.raise_for_status()
    return response.json()

# Main ICRL Execution
def main():
    dfs = load_all_campaigns(FOLDER_PATH)

    # Initialize ICRL ranker
    icrl_ranker = ICRLAlgorithmRanker()

    print("Starting ICRL Algorithm Ranking System")
    print("=" * 60)

    for fname, df in dfs.items():
        print(f"\n ICRL Analysis for: {fname}")
        print("="*80)

        try:
            # Parse campaign data
            parsed_df = icrl_ranker.parse_campaign_data(df)

            # Build system prompt
            system_prompt = icrl_ranker.build_icrl_system_prompt()

            # Build user prompt with context
            user_prompt = icrl_ranker.build_algorithm_ranking_prompt(parsed_df)

            # Make prediction
            result = call_minerva_icrl(system_prompt, user_prompt)

            if 'data' in result and len(result['data']) > 0:
                prediction = result['data'][0]

                print("\nALGORITHM RANKING PREDICTION:")
                print("-" * 50)
                print(prediction)

                # Calculate reward-based feedback
                reward_data = icrl_ranker.calculate_reward(parsed_df)
                prediction_quality = icrl_ranker.evaluate_prediction_quality(prediction, reward_data)

                print(f"\n REWARD ANALYSIS:")
                print("-" * 30)
                print(f"Total Reward: {reward_data['total_reward']:.3f}")
                print(f"Average Reward: {reward_data['average_reward']:.3f}")
                print(f"Prediction Quality Score: {prediction_quality:.3f}")

                # Show top 3 algorithms by reward
                sorted_algorithms = sorted(
                    reward_data['algorithm_rewards'].items(),
                    key=lambda x: x[1]['reward'],
                    reverse=True
                )[:3]

                print(f"\nTOP 3 ALGORITHMS BY REWARD:")
                for i, (alg, data) in enumerate(sorted_algorithms, 1):
                    print(f"{i}. {alg}: Reward={data['reward']:.3f} "
                          f"(Rev={data['normalized_revenue']:.3f}, "
                          f"Conv={data['normalized_conversion_rate']:.3f})")

                # Create scalar feedback for ICRL
                scalar_feedback = prediction_quality
                feedback_text = f"Prediction quality score: {scalar_feedback:.3f}. "

                if scalar_feedback > 0.7:
                    feedback_text += "Excellent ranking alignment with reward data."
                elif scalar_feedback > 0.4:
                    feedback_text += "Good ranking with room for improvement."
                else:
                    feedback_text += "Ranking needs significant improvement."

                print(f"\n SCALAR FEEDBACK: {feedback_text}")

                # Add actual campaign data to context for future learning
                icrl_ranker.add_campaign_example(
                    campaign_name=fname,
                    reward_data=reward_data
                )

                print(f"\nCampaign Examples in Context: {len(icrl_ranker.context_examples)}")

            else:
                print("No valid response received")

        except Exception as e:
            print(f"Error processing {fname}: {e}")
            print(f"Error details: {str(e)}")

    print(f"\nICRL Learning Complete!")
    print(f"Total context examples learned: {len(icrl_ranker.context_examples)}")

if __name__ == "__main__":
    main()
