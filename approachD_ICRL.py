import os
import json
import pandas as pd
import requests

# Configuration
FOLDER_PATH = '/Users/<USER>/Downloads/Ebebek Campaign Statistics'
API_URL = "http://sirius.api.staging.internal.minerva/text/v1/generate"

HEADERS = {
    "x-api-key": "minerva-test",
    "User-Agent": "insider/dev",
    "Content-Type": "application/json"
}

# Extract variation-level data from JSON
def extract_campaign_data(data):
    rows = []
    for campaign in data.get("campaigns", {}).get("details", []):
        for var in campaign.get("nested", []):
            var['campaignName'] = campaign.get('campaignName')
            var['builderId'] = campaign.get('builderId')
            var['pageType'] = campaign.get('pageType')
            var['platform'] = campaign.get('platform')
            var['status'] = campaign.get('status')
            var['locale'] = campaign.get('locale')
            rows.append(var)
    return pd.json_normalize(rows)

# Load all JSON files
def load_all_campaigns(folder_path):
    dfs = {}
    for filename in os.listdir(folder_path):
        if filename.lower().endswith('.json'):
            path = os.path.join(folder_path, filename)
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            df = extract_campaign_data(data)
            dfs[filename] = df
    return dfs

# ICRL Algorithm Ranker
class ICRLAlgorithmRanker:
    def __init__(self):
        self.context_examples = []
        self.max_context_examples = 5  # Keep latest 5 examples for context

    def parse_campaign_data(self, df):
        df = df.copy()

        def parse_percent(p):
            try:
                return float(str(p).replace("%", "").strip()) / 100
            except:
                return 0.0

        def parse_currency(c):
            try:
                return float(str(c).replace(",", "").replace("TRY", "").strip())
            except:
                return 0.0

        df['parsed_conversion_rate'] = df['conversionRate'].apply(parse_percent)
        df['parsed_incremental_revenue'] = df['incrementalRevenueFormatted'].apply(parse_currency)
        df['parsed_impressions'] = pd.to_numeric(df['impression'], errors='coerce').fillna(0).astype(int)

        # New features
        df['parsed_click_rate'] = df.get('clickRate', 0).apply(parse_percent) if 'clickRate' in df else 0.0
        df['parsed_add_to_cart_rate'] = df.get('addToCartRate', 0).apply(parse_percent) if 'addToCartRate' in df else 0.0

        df['success_score'] = (df['parsed_conversion_rate'] * 100) + (df['parsed_incremental_revenue'] / 1000)
        df['engagement_score'] = (df['parsed_click_rate'] * 100) + (df['parsed_add_to_cart_rate'] * 100)

        # Combine scores: You can tune these weights
        alpha = 0.5  # weight for success_score
        beta = 0.5   # weight for engagement_score
        df['final_score'] = (alpha * df['success_score']) + (beta * df['engagement_score'])

        return df

    def build_icrl_system_prompt(self):
        """Build ICRL-compatible system prompt that emphasizes learning from enhanced scoring feedback."""
        return """You are an expert algorithm analyst. Answer the following questions about algorithm ranking. The feedback you receive will be the actual performance scores achieved by the algorithms you predict. You must learn from these numerical feedback values to make better algorithm predictions.

Task: Rank algorithms by their expected performance scores.

Enhanced Scoring System:
- Success Score = (Conversion Rate * 100) + (Incremental Revenue / 1000)
- Engagement Score = (Click Rate * 100) + (Add to Cart Rate * 100)
- Final Score = (0.5 * Success Score) + (0.5 * Engagement Score)

The feedback will show you the actual final scores that your predicted algorithms achieved. Use this numerical feedback to learn which algorithms perform best overall, considering both conversion/revenue performance and user engagement patterns."""

    def build_context_from_history(self):
        if not self.context_examples:
            return ""

        context = "\nPREVIOUS ALGORITHM PERFORMANCE FEEDBACK\n" + "=" * 50 + "\n"

        for example in self.context_examples[-self.max_context_examples:]:
            # Show ALL algorithms from this dataset
            all_algorithms = sorted(example['variations'], key=lambda x: x.get('final_score', x['success_score']), reverse=True)

            for var in all_algorithms:
                context += f"Algorithm: {var['algorithm']}\n"
                context += f"Performance Score: {var.get('final_score', var['success_score']):.2f}\n"
                context += f"Success Score: {var['success_score']:.2f}, Engagement Score: {var.get('engagement_score', 0):.2f}\n"
                context += f"Metrics: CR={var['conversionRate']}, Revenue={var['incrementalRevenue']}, Impressions={var['impression']}\n\n"

            context += "-" * 40 + "\n"

        return context + "Learn from these algorithm performance scores to rank algorithms by their expected performance.\n"

    def rank_algorithm_variations(self, df):
        """
        Rank algorithm variations using success score calculation.
        Success Score = (Conversion Rate * 100) + (Incremental Revenue / 1000)

        Args:
            df: DataFrame with campaign variation data

        Returns:
            list: Top 15 variations as dictionaries with ranking information
        """
        # Sort by success score descending
        df_sorted = df.sort_values(by='success_score', ascending=False)

        # Get top 15 variations
        top_variations = []

        for rank, (_, row) in enumerate(df_sorted.head(15).iterrows(), 1):
            variation = {
                'rank': rank,
                'variationId': row.get('variationId', 'N/A'),
                'algorithm': row.get('algorithm', 'N/A'),
                'campaignName': row.get('campaignName', 'N/A'),
                'platform': row.get('platform', 'N/A'),
                'pageType': row.get('pageType', 'N/A'),
                'conversionRate': row.get('conversionRate', 'N/A'),
                'incrementalRevenue': row.get('incrementalRevenueFormatted', 'N/A'),
                'impressions': row.get('impression', 'N/A'),
                'success_score': round(row.get('success_score', 0), 2)
            }
            top_variations.append(variation)

        return top_variations

    def build_algorithm_ranking_prompt(self, df):
        """
        Build ICRL-style prompt that asks for top 16 algorithm ranking with reasoning.

        Args:
            df: DataFrame with campaign variation data

        Returns:
            str: ICRL-compatible prompt asking for algorithm ranking
        """
        context_history = self.build_context_from_history()

        # Get unique algorithms available in this dataset
        available_algorithms = df['algorithm'].unique().tolist()

        prompt = f"""{context_history}
CURRENT TASK:
Rank the top 16 algorithms that will achieve the highest performance scores.

Available Algorithms: {', '.join(available_algorithms)}

Enhanced Scoring System:
- Success Score = (Conversion Rate * 100) + (Incremental Revenue / 1000)
- Engagement Score = (Click Rate * 100) + (Add to Cart Rate * 100)
- Final Score = (0.5 * Success Score) + (0.5 * Engagement Score)

Based on the previous feedback showing actual final performance scores achieved by different algorithms, provide:

1. REASONING: Explain your analysis of which algorithms perform best based on the performance patterns you've learned from the feedback data.

2. TOP 16 ALGORITHM RANKING: List exactly 16 algorithms in order of predicted performance (highest to lowest performance score).

Format your response as:
REASONING: [Your detailed reasoning here]

RANKING:
1. [Algorithm Name]
2. [Algorithm Name]
...
16. [Algorithm Name]"""

        return prompt

    def add_campaign_example(self, campaign_name, df):
        """Add campaign example to context, keeping only the latest 5 examples."""
        variations = []
        for _, row in df.iterrows():
            variations.append({
                "variationId": row.get("variationId"),
                "algorithm": row.get("algorithm"),
                "platform": row.get("platform"),
                "pageType": row.get("pageType"),
                "conversionRate": row.get("conversionRate"),
                "incrementalRevenue": row.get("incrementalRevenueFormatted"),
                "impression": row.get("impression"),
                "success_score": row.get("success_score", 0.0),
                "engagement_score": row.get("engagement_score", 0.0),
                "final_score": row.get("final_score", row.get("success_score", 0.0))
            })

        # Add new example
        self.context_examples.append({
            "campaign_name": campaign_name,
            "variations": variations
        })

        # Keep only the latest 5 examples
        if len(self.context_examples) > self.max_context_examples:
            self.context_examples = self.context_examples[-self.max_context_examples:]

# API caller
def call_minerva_icrl(system_prompt, user_prompt):
    payload = {
        "system_prompt": system_prompt,
        "user_prompt": user_prompt,
        "num_variations": 1,
        "temperature": 0.4,
        "max_tokens": 2048,
        "model": "openai/gpt-3.5-turbo",
        "json_mode": False
    }
    response = requests.post(API_URL, headers=HEADERS, json=payload)
    response.raise_for_status()
    return response.json()

# Main
def main():
    dfs = load_all_campaigns(FOLDER_PATH)
    icrl = ICRLAlgorithmRanker()

    print("\nICRL Campaign Variation Ranking\n" + "=" * 60)
    print(f"\nFound {len(dfs)} campaign files:")

    # Sort filenames by date (earliest to latest)
    sorted_filenames = sorted(dfs.keys())

    for filename in sorted_filenames:
        print(f"  - {filename}")
    print()

    # Process files in chronological order
    for fname in sorted_filenames:
        df = dfs[fname]
        print(f"\nProcessing file: {fname}")
        try:
            parsed_df = icrl.parse_campaign_data(df)

            # Build prompts for LLM
            system_prompt = icrl.build_icrl_system_prompt()
            user_prompt = icrl.build_algorithm_ranking_prompt(parsed_df)

            # Make API call to get algorithm ranking with reasoning
            result = call_minerva_icrl(system_prompt, user_prompt)

            if 'data' in result and result['data']:
                prediction = result['data'][0]

                print("\nLLM ALGORITHM RANKING WITH REASONING:")
                print("-" * 60)
                print(prediction)
                print()

                # Add campaign data to context for future learning
                icrl.add_campaign_example(fname, parsed_df)
                print(f"✓ Added campaign to context. Context now contains {len(icrl.context_examples)} examples.")

            else:
                print("❌ No valid response from LLM API.")

        except Exception as e:
            print(f"Error processing {fname}: {str(e)}")

    print("\nFinished processing all campaign files.")
    print(f"Total context examples stored: {len(icrl.context_examples)}")

if __name__ == "__main__":
    main()


