import os
import json
import pandas as pd
import requests

# === Configuration ===
FOLDER_PATH = '/Users/<USER>/Downloads/Ebebek Campaign Statistics'
API_URL = "http://sirius.api.staging.internal.minerva/text/v1/generate"
HEADERS = {
    "x-api-key": "minerva-test",
    "User-Agent": "insider/dev",
    "Content-Type": "application/json"
}

# === Extract variation-level data from JSON ===
def extract_campaign_data(data):
    rows = []
    for campaign in data.get("campaigns", {}).get("details", []):
        for var in campaign.get("nested", []):
            var['campaignName'] = campaign.get('campaignName')
            var['builderId'] = campaign.get('builderId')
            var['pageType'] = campaign.get('pageType')
            var['platform'] = campaign.get('platform')
            var['status'] = campaign.get('status')
            var['locale'] = campaign.get('locale')
            rows.append(var)
    return pd.json_normalize(rows)

# === Load all JSON files ===
def load_all_campaigns(folder_path):
    dfs = {}
    for filename in os.listdir(folder_path):
        if filename.lower().endswith('.json'):
            path = os.path.join(folder_path, filename)
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            df = extract_campaign_data(data)
            dfs[filename] = df
    return dfs

# === ICRL Framework Implementation ===
def build_icrl_prompt(df):
    """
    ICRL Framework:
    I - Instruction: Clear, specific task definition
    C - Context: Relevant background and data
    R - Response: Expected output format
    L - Learning: Examples and best practices
    """
    
    # Parse and prepare data
    df = df.copy()
    
    def parse_percent(p):
        try:
            return float(str(p).replace("%", "").strip())
        except:
            return 0.0
    
    def parse_currency(c):
        try:
            return float(str(c).replace(",", "").replace("TRY", "").strip())
        except:
            return 0.0
    
    df['parsed_conversion_rate'] = df['conversionRate'].apply(parse_percent)
    df['parsed_incremental_revenue'] = df['incrementalRevenueFormatted'].apply(parse_currency)
    df['parsed_impressions'] = pd.to_numeric(df['impression'], errors='coerce').fillna(0).astype(int)
    
    # I - INSTRUCTION
    instruction = """
TASK: Generate a prioritized list of recommendation algorithms for e-commerce campaigns based on performance data analysis.

OBJECTIVE: Rank algorithms by effectiveness using conversion rate and incremental revenue metrics to optimize campaign performance.
"""
    
    # C - CONTEXT
    context = f"""
CONTEXT:
- Dataset: E-commerce campaign performance data from Ebebek
- Metrics Available: Conversion Rate, Incremental Revenue, Impressions
- Business Goal: Maximize both conversion rate and revenue generation
- Campaign Type: Product recommendation algorithms
- Total Algorithms in Dataset: {len(df)}
- Date Range: Campaign performance data

PERFORMANCE DATA:
"""
    
    # Add data rows to context
    for _, row in df.iterrows():
        context += f"""
Algorithm: {row.get('algorithm', 'N/A')}
- Conversion Rate: {row.get('conversionRate', 'N/A')}
- Incremental Revenue: {row.get('incrementalRevenueFormatted', 'N/A')}
- Impressions: {row.get('impression', 'N/A')}
"""
    
    # R - RESPONSE FORMAT
    response_format = """
REQUIRED RESPONSE FORMAT:

1. RANKING ANALYSIS:
   - Rank algorithms by conversion rate (highest to lowest)
   - Rank algorithms by incremental revenue (highest to lowest)
   - Calculate composite performance score

2. JSON OUTPUT:
   Provide a JSON array with at least 10-20 algorithms in this exact format:
   ```json
   [
     {
       "algorithm": "Algorithm Name",
       "conversion_rate": 0.25,
       "incremental_revenue": 25958.87,
       "impressions": 26768,
       "performance_score": 85.2,
       "rank": 1
     }
   ]
   ```

3. STRATEGIC RECOMMENDATIONS:
   - Top 3 algorithms with justification
   - Fallback algorithms if data is insufficient
   - Implementation priority order
"""
    
    # L - LEARNING (Examples and Best Practices)
    learning = """
BEST PRACTICES & EXAMPLES:

SCORING METHODOLOGY:
- Performance Score = (Conversion Rate × 40) + (Revenue Impact × 30) + (Impression Volume × 20) + (Consistency × 10)
- Revenue Impact = Incremental Revenue / 1000 (normalized)
- Impression Volume = log(impressions) for scale normalization

ALGORITHM CATEGORIES TO CONSIDER:
1. Behavioral: "User Based", "Purchased Together", "Viewed Together"
2. Content-Based: "Complementary Products", "Similar Products"
3. Popularity-Based: "Most Popular", "Trending Products", "Top Sellers"
4. Hybrid: "Mixed Strategy", "Personalized Recommendations"
5. Fallback: "New Arrivals", "Featured Products", "Category Best Sellers"

DECISION CRITERIA:
- High conversion rate (>0.15%) = Priority Tier 1
- Significant revenue impact (>10,000 TRY) = Priority Tier 1
- Balanced performance across metrics = Priority Tier 2
- Low/zero performance = Tier 3 or exclude

EXAMPLE GOOD RESPONSE:
"Purchased Together algorithm ranks #1 with 0.25% conversion rate and 25,958 TRY incremental revenue, demonstrating strong cross-selling effectiveness..."
"""
    
    # Combine all ICRL components
    full_prompt = f"{instruction}\n{context}\n{response_format}\n{learning}"
    
    return full_prompt

# === API Caller ===
def call_minerva_icrl(system_prompt, user_prompt):
    payload = {
        "system_prompt": system_prompt,
        "user_prompt": user_prompt,
        "num_variations": 1,
        "temperature": 0.3,  # Lower temperature for more consistent analysis
        "max_tokens": 2048,
        "model": "openai/gpt-3.5-turbo",
        "json_mode": False
    }
    response = requests.post(API_URL, headers=HEADERS, json=payload)
    response.raise_for_status()
    return response.json()

# === Main Execution ===
def main():
    dfs = load_all_campaigns(FOLDER_PATH)
    
    system_prompt = """You are a senior e-commerce data scientist and marketing analyst with expertise in recommendation algorithms and campaign optimization. You excel at data-driven decision making and strategic algorithm selection."""
    
    for fname, df in dfs.items():
        print(f"\n🎯 ICRL Analysis for: {fname}")
        print("="*80)
        
        try:
            icrl_prompt = build_icrl_prompt(df)
            result = call_minerva_icrl(system_prompt, icrl_prompt)
            
            print("\n📊 ALGORITHM ANALYSIS RESULTS:")
            print("-" * 50)
            
            for i, variation in enumerate(result.get("data", [])):
                print(f"\nAnalysis {i+1}:\n{variation}")
                
        except Exception as e:
            print(f"❌ Error processing {fname}: {e}")
            print(f"Error details: {str(e)}")

if __name__ == "__main__":
    main()
