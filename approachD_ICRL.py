import os
import json
import pandas as pd
import requests

# Configuration
FOLDER_PATH = '/Users/<USER>/Downloads/Ebebek Campaign Statistics'
API_URL = "http://sirius.api.staging.internal.minerva/text/v1/generate"

HEADERS = {
    "x-api-key": "minerva-test",
    "User-Agent": "insider/dev",
    "Content-Type": "application/json"
}

# Extract variation-level data from JSON
def extract_campaign_data(data):
    rows = []
    for campaign in data.get("campaigns", {}).get("details", []):
        for var in campaign.get("nested", []):
            var['campaignName'] = campaign.get('campaignName')
            var['builderId'] = campaign.get('builderId')
            var['pageType'] = campaign.get('pageType')
            var['platform'] = campaign.get('platform')
            var['status'] = campaign.get('status')
            var['locale'] = campaign.get('locale')
            rows.append(var)
    return pd.json_normalize(rows)

# Load all JSON files
def load_all_campaigns(folder_path):
    dfs = {}
    for filename in os.listdir(folder_path):
        if filename.lower().endswith('.json'):
            path = os.path.join(folder_path, filename)
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            df = extract_campaign_data(data)
            dfs[filename] = df
    return dfs

# ICRL Algorithm Ranker
class ICRLAlgorithmRanker:
    def __init__(self):
        self.context_examples = []
        self.max_context_examples = 10

    def parse_campaign_data(self, df):
        df = df.copy()

        def parse_percent(p):
            try:
                return float(str(p).replace("%", "").strip()) / 100
            except:
                return 0.0

        def parse_currency(c):
            try:
                return float(str(c).replace(",", "").replace("TRY", "").strip())
            except:
                return 0.0

        df['parsed_conversion_rate'] = df['conversionRate'].apply(parse_percent)
        df['parsed_incremental_revenue'] = df['incrementalRevenueFormatted'].apply(parse_currency)
        df['parsed_impressions'] = pd.to_numeric(df['impression'], errors='coerce').fillna(0).astype(int)
        df['success_score'] = (df['parsed_conversion_rate'] * 100) + (df['parsed_incremental_revenue'] / 1000)

        return df

    def build_icrl_system_prompt(self):
        return """You are an expert e-commerce algorithm analyst. Your task is to rank campaign variations by success score.
Each variation uses a single algorithm and is defined by its campaign context.

Success Score = (Conversion Rate * 100) + (Incremental Revenue / 1000)

Your job is to:
1. Rank algorithm-campaign-variation combinations based on success score.
2. Prefer variations with higher impressions if scores are tied.
3. Learn from context history to improve future rankings.
"""

    def build_context_from_history(self):
        if not self.context_examples:
            return ""

        context = "\nPREVIOUS CAMPAIGN VARIATION EXAMPLES\n" + "=" * 50 + "\n"

        for i, example in enumerate(self.context_examples[-self.max_context_examples:]):
            context += f"\nExample {i+1}: {example['campaign_name']}\n"
            for var in example['variations']:
                context += f"- Variation ID: {var['variationId']}\n"
                context += f"  Algorithm: {var['algorithm']}\n"
                context += f"  Platform: {var['platform']} | Page: {var['pageType']}\n"
                context += f"  CR: {var['conversionRate']} | Revenue: {var['incrementalRevenue']} TRY\n"
                context += f"  Impressions: {var['impression']} | Success Score: {var['success_score']:.2f}\n"
            context += "-" * 40 + "\n"

        return context + "\nUse these examples to inform future rankings.\n"

    def rank_algorithm_variations(self, df):
        """
        Rank algorithm variations using success score calculation.
        Success Score = (Conversion Rate * 100) + (Incremental Revenue / 1000)

        Args:
            df: DataFrame with campaign variation data

        Returns:
            list: Top 15 variations as dictionaries with ranking information
        """
        # Sort by success score descending
        df_sorted = df.sort_values(by='success_score', ascending=False)

        # Get top 15 variations
        top_variations = []

        for rank, (_, row) in enumerate(df_sorted.head(15).iterrows(), 1):
            variation = {
                'rank': rank,
                'variationId': row.get('variationId', 'N/A'),
                'algorithm': row.get('algorithm', 'N/A'),
                'campaignName': row.get('campaignName', 'N/A'),
                'platform': row.get('platform', 'N/A'),
                'pageType': row.get('pageType', 'N/A'),
                'conversionRate': row.get('conversionRate', 'N/A'),
                'incrementalRevenue': row.get('incrementalRevenueFormatted', 'N/A'),
                'impressions': row.get('impression', 'N/A'),
                'success_score': round(row.get('success_score', 0), 2)
            }
            top_variations.append(variation)

        return top_variations

    def add_campaign_example(self, campaign_name, df):
        variations = []
        for _, row in df.iterrows():
            variations.append({
                "variationId": row.get("variationId"),
                "algorithm": row.get("algorithm"),
                "platform": row.get("platform"),
                "pageType": row.get("pageType"),
                "conversionRate": row.get("conversionRate"),
                "incrementalRevenue": row.get("incrementalRevenueFormatted"),
                "impression": row.get("impression"),
                "success_score": row.get("success_score", 0.0)
            })
        self.context_examples.append({
            "campaign_name": campaign_name,
            "variations": variations
        })
        if len(self.context_examples) > self.max_context_examples:
            self.context_examples = self.context_examples[-self.max_context_examples:]

# API caller
def call_minerva_icrl(system_prompt, user_prompt):
    payload = {
        "system_prompt": system_prompt,
        "user_prompt": user_prompt,
        "num_variations": 1,
        "temperature": 0.4,
        "max_tokens": 2048,
        "model": "openai/gpt-3.5-turbo",
        "json_mode": False
    }
    response = requests.post(API_URL, headers=HEADERS, json=payload)
    response.raise_for_status()
    return response.json()

# Main
def main():
    dfs = load_all_campaigns(FOLDER_PATH)
    icrl = ICRLAlgorithmRanker()

    print("\nICRL Campaign Variation Ranking\n" + "=" * 60)

    for fname, df in dfs.items():
        print(f"\nProcessing file: {fname}")
        try:
            parsed_df = icrl.parse_campaign_data(df)

            # Build prompts
            system_prompt = icrl.build_icrl_system_prompt()
            user_prompt = icrl.build_algorithm_ranking_prompt(parsed_df)

            # Make API call
            result = call_minerva_icrl(system_prompt, user_prompt)

            # Display and process response
            if 'data' in result and result['data']:
                print("\nLLM Prediction Output:\n" + "-" * 50)
                print(result['data'][0])
                icrl.add_campaign_example(fname, parsed_df)
                print("\nAdded variation data to context.")
            else:
                print("No valid data returned by LLM.")

        except Exception as e:
            print(f"Error processing {fname}: {str(e)}")

    print("\nFinished processing all campaign files.")
    print(f"Total context examples stored: {len(icrl.context_examples)}")

if __name__ == "__main__":
    main()



