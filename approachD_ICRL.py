import os
import json
import pandas as pd
import requests

# Configuration
FOLDER_PATH = '/Users/<USER>/Downloads/Ebebek Campaign Statistics'
API_URL = "http://sirius.api.staging.internal.minerva/text/v1/generate"

HEADERS = {
    "x-api-key": "minerva-test",
    "User-Agent": "insider/dev",
    "Content-Type": "application/json"
}

# Extract variation-level data from JSON
def extract_campaign_data(data):
    rows = []
    for campaign in data.get("campaigns", {}).get("details", []):
        for var in campaign.get("nested", []):
            var['campaignName'] = campaign.get('campaignName')
            var['builderId'] = campaign.get('builderId')
            var['pageType'] = campaign.get('pageType')
            var['platform'] = campaign.get('platform')
            var['status'] = campaign.get('status')
            var['locale'] = campaign.get('locale')
            rows.append(var)
    return pd.json_normalize(rows)

# Load all JSON files
def load_all_campaigns(folder_path):
    dfs = {}
    for filename in os.listdir(folder_path):
        if filename.lower().endswith('.json'):
            path = os.path.join(folder_path, filename)
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            df = extract_campaign_data(data)
            dfs[filename] = df
    return dfs

# ICRL Algorithm Ranker
class ICRLAlgorithmRanker:
    def __init__(self):
        self.context_examples = []
        self.max_context_examples = 10

    def parse_campaign_data(self, df):
        df = df.copy()

        def parse_percent(p):
            try:
                return float(str(p).replace("%", "").strip()) / 100
            except:
                return 0.0

        def parse_currency(c):
            try:
                return float(str(c).replace(",", "").replace("TRY", "").strip())
            except:
                return 0.0

        df['parsed_conversion_rate'] = df['conversionRate'].apply(parse_percent)
        df['parsed_incremental_revenue'] = df['incrementalRevenueFormatted'].apply(parse_currency)
        df['parsed_impressions'] = pd.to_numeric(df['impression'], errors='coerce').fillna(0).astype(int)
        df['success_score'] = (df['parsed_conversion_rate'] * 100) + (df['parsed_incremental_revenue'] / 1000)

        return df

    def build_icrl_system_prompt(self):
        """Build ICRL-compatible system prompt that emphasizes learning from success score feedback."""
        return """You are an expert marketing analyst. Answer the following questions about algorithm ranking. The feedback you receive will be the actual success score achieved by the algorithm you predict. You must learn from these success score feedback values to make better algorithm predictions.

Task: Predict which algorithm will achieve the highest success score for a given campaign context.

Success Score Formula: (Conversion Rate * 100) + (Incremental Revenue / 1000)

Use this numerical feedback based on previous campaigns to learn which algorithms perform best in different campaign contexts and improve your future predictions."""

    def build_context_from_history(self):
        if not self.context_examples:
            return ""

        context = "\nPREVIOUS ALGORITHM PREDICTIONS AND FEEDBACK\n" + "=" * 50 + "\n"

        for example in self.context_examples[-self.max_context_examples:]:
            context += f"\nCampaign: {example['campaign_name']}\n"

            # Show top 3 algorithms from this campaign as examples
            top_algorithms = sorted(example['variations'], key=lambda x: x['success_score'], reverse=True)[:3]

            for var in top_algorithms:
                context += f"Question: Which algorithm will perform best for {var['platform']} {var['pageType']} campaign?\n"
                context += f"Prediction: {var['algorithm']}\n"
                context += f"Feedback: {var['success_score']:.2f} (success score achieved)\n"
                context += f"Context: CR={var['conversionRate']}, Revenue={var['incrementalRevenue']}, Impressions={var['impression']}\n\n"

            context += "-" * 40 + "\n"

        return context + "Learn from these prediction-feedback pairs to improve future algorithm selection.\n"

    def rank_algorithm_variations(self, df):
        """
        Rank algorithm variations using success score calculation.
        Success Score = (Conversion Rate * 100) + (Incremental Revenue / 1000)

        Args:
            df: DataFrame with campaign variation data

        Returns:
            list: Top 15 variations as dictionaries with ranking information
        """
        # Sort by success score descending
        df_sorted = df.sort_values(by='success_score', ascending=False)

        # Get top 15 variations
        top_variations = []

        for rank, (_, row) in enumerate(df_sorted.head(15).iterrows(), 1):
            variation = {
                'rank': rank,
                'variationId': row.get('variationId', 'N/A'),
                'algorithm': row.get('algorithm', 'N/A'),
                'campaignName': row.get('campaignName', 'N/A'),
                'platform': row.get('platform', 'N/A'),
                'pageType': row.get('pageType', 'N/A'),
                'conversionRate': row.get('conversionRate', 'N/A'),
                'incrementalRevenue': row.get('incrementalRevenueFormatted', 'N/A'),
                'impressions': row.get('impression', 'N/A'),
                'success_score': round(row.get('success_score', 0), 2)
            }
            top_variations.append(variation)

        return top_variations

    def build_icrl_prediction_prompt(self, campaign_context):
        """
        Build ICRL-style prompt that asks for algorithm prediction for a specific campaign context.
        This follows the ICRL pattern of asking a question that can receive numerical feedback.

        Args:
            campaign_context: Dictionary with campaign details (platform, pageType, etc.)

        Returns:
            str: ICRL-compatible prompt asking for algorithm prediction
        """
        context_history = self.build_context_from_history()

        prompt = f"""{context_history}
CURRENT QUESTION:
Which algorithm will achieve the highest success score for this campaign context?

Campaign Details:
- Platform: {campaign_context.get('platform', 'N/A')}
- Page Type: {campaign_context.get('pageType', 'N/A')}
- Campaign Name: {campaign_context.get('campaignName', 'N/A')}
- Locale: {campaign_context.get('locale', 'N/A')}

Available algorithms in dataset: {', '.join(campaign_context.get('available_algorithms', []))}

Based on the previous feedback showing actual success scores achieved, predict which algorithm will perform best.

Answer: """

        return prompt

    def add_campaign_example(self, campaign_name, df):
        variations = []
        for _, row in df.iterrows():
            variations.append({
                "variationId": row.get("variationId"),
                "algorithm": row.get("algorithm"),
                "platform": row.get("platform"),
                "pageType": row.get("pageType"),
                "conversionRate": row.get("conversionRate"),
                "incrementalRevenue": row.get("incrementalRevenueFormatted"),
                "impression": row.get("impression"),
                "success_score": row.get("success_score", 0.0)
            })
        self.context_examples.append({
            "campaign_name": campaign_name,
            "variations": variations
        })
        if len(self.context_examples) > self.max_context_examples:
            self.context_examples = self.context_examples[-self.max_context_examples:]

# API caller
def call_minerva_icrl(system_prompt, user_prompt):
    payload = {
        "system_prompt": system_prompt,
        "user_prompt": user_prompt,
        "num_variations": 1,
        "temperature": 0.4,
        "max_tokens": 2048,
        "model": "openai/gpt-3.5-turbo",
        "json_mode": False
    }
    response = requests.post(API_URL, headers=HEADERS, json=payload)
    response.raise_for_status()
    return response.json()

# Main
def main():
    dfs = load_all_campaigns(FOLDER_PATH)
    icrl = ICRLAlgorithmRanker()

    print("\nICRL Campaign Variation Ranking\n" + "=" * 60)
    print(f"\nFound {len(dfs)} campaign files:")
    for filename in dfs.keys():
        print(f"  - {filename}")
    print()

    for fname, df in dfs.items():
        print(f"\nProcessing file: {fname}")
        try:
            parsed_df = icrl.parse_campaign_data(df)

            # Directly rank variations using the function
            top_variations = icrl.rank_algorithm_variations(parsed_df)

            print("\nTOP 15 ALGORITHM VARIATIONS:")
            print("-" * 50)

            # Display results in a formatted way
            for variation in top_variations:
                print(f"#{variation['rank']} - {variation['algorithm']}")
                print(f"   Campaign: {variation['campaignName']}")
                print(f"   Platform: {variation['platform']} | Page: {variation['pageType']}")
                print(f"   Conversion Rate: {variation['conversionRate']}")
                print(f"   Revenue: {variation['incrementalRevenue']}")
                print(f"   Impressions: {variation['impressions']}")
                print(f"   Success Score: {variation['success_score']}")
                print()

            # Add to context for future learning
            icrl.add_campaign_example(fname, parsed_df)
            print(f"Added {len(top_variations)} variations to context.")

        except Exception as e:
            print(f"Error processing {fname}: {str(e)}")

    print("\nFinished processing all campaign files.")
    print(f"Total context examples stored: {len(icrl.context_examples)}")

if __name__ == "__main__":
    main()


