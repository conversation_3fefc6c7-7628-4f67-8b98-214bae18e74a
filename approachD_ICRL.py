import os
import json
import pandas as pd
import requests

# === Configuration ===
FOLDER_PATH = '/Users/<USER>/Downloads/Ebebek Campaign Statistics'
API_URL = "http://sirius.api.staging.internal.minerva/text/v1/generate"
HEADERS = {
    "x-api-key": "minerva-test",
    "User-Agent": "insider/dev",
    "Content-Type": "application/json"
}

# === Extract variation-level data from JSON ===
def extract_campaign_data(data):
    rows = []
    for campaign in data.get("campaigns", {}).get("details", []):
        for var in campaign.get("nested", []):
            var['campaignName'] = campaign.get('campaignName')
            var['builderId'] = campaign.get('builderId')
            var['pageType'] = campaign.get('pageType')
            var['platform'] = campaign.get('platform')
            var['status'] = campaign.get('status')
            var['locale'] = campaign.get('locale')
            rows.append(var)
    return pd.json_normalize(rows)

# === Load all JSON files ===
def load_all_campaigns(folder_path):
    dfs = {}
    for filename in os.listdir(folder_path):
        if filename.lower().endswith('.json'):
            path = os.path.join(folder_path, filename)
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            df = extract_campaign_data(data)
            dfs[filename] = df
    return dfs

# === ICRL (In-Context Reinforcement Learning) Implementation ===
class ICRLAlgorithmRanker:
    """
    ICRL-based algorithm ranking system that learns from feedback.

    The system makes predictions about algorithm rankings, receives feedback
    on the quality of those predictions, and uses that feedback to improve
    future rankings through in-context learning.
    """

    def __init__(self):
        self.context_examples = []  # Store (prompt, prediction, feedback) tuples
        self.max_context_examples = 10  # Limit context size

    def parse_campaign_data(self, df):
        """Parse and prepare campaign data for analysis."""
        df = df.copy()

        def parse_percent(p):
            try:
                return float(str(p).replace("%", "").strip())
            except:
                return 0.0

        def parse_currency(c):
            try:
                return float(str(c).replace(",", "").replace("TRY", "").strip())
            except:
                return 0.0

        df['parsed_conversion_rate'] = df['conversionRate'].apply(parse_percent)
        df['parsed_incremental_revenue'] = df['incrementalRevenueFormatted'].apply(parse_currency)
        df['parsed_impressions'] = pd.to_numeric(df['impression'], errors='coerce').fillna(0).astype(int)

        return df

    def build_icrl_system_prompt(self):
        """Build the system prompt for ICRL algorithm ranking."""
        return """You are an expert e-commerce algorithm analyst. Your task is to rank recommendation algorithms based on performance data.

You will receive campaign performance data and must predict which algorithms will perform best. Feedback will indicate if your rankings are accurate. Use this feedback to improve your future predictions.

Focus on:
1. Conversion rate effectiveness
2. Revenue generation potential
3. Impression volume and reach
4. Overall business impact

Learn from feedback to make better algorithm recommendations."""

    def build_context_from_history(self):
        """Build context from previous examples and feedback."""
        if not self.context_examples:
            return ""

        context = "\n=== PREVIOUS EXAMPLES AND FEEDBACK ===\n"

        for i, (prompt, prediction, feedback) in enumerate(self.context_examples[-self.max_context_examples:]):
            context += f"\nExample {i+1}:\n"
            context += f"Data: {prompt[:200]}...\n"  # Truncate for brevity
            context += f"Prediction: {prediction[:300]}...\n"
            context += f"Feedback: {feedback}\n"
            context += "-" * 50 + "\n"

        context += "\nUse the above examples and feedback to improve your current prediction.\n"
        context += "=" * 60 + "\n\n"

        return context

    def build_algorithm_ranking_prompt(self, df):
        """Build the main prompt for algorithm ranking."""
        context_history = self.build_context_from_history()

        prompt = f"""{context_history}CURRENT TASK: Rank the following algorithms by expected performance.

PERFORMANCE DATA:
"""

        # Add algorithm data
        for _, row in df.iterrows():
            prompt += f"""
Algorithm: {row.get('algorithm', 'N/A')}
- Conversion Rate: {row.get('conversionRate', 'N/A')}
- Incremental Revenue: {row.get('incrementalRevenueFormatted', 'N/A')}
- Impressions: {row.get('impression', 'N/A')}
"""

        prompt += """
REQUIRED OUTPUT FORMAT:
Provide a ranked list in this exact JSON format:
```json
[
  {
    "rank": 1,
    "algorithm": "Algorithm Name",
    "conversion_rate": "X.XX%",
    "incremental_revenue": "X,XXX TRY",
    "impressions": XXXX,
    "performance_score": XX.X,
    "reasoning": "Brief explanation for ranking"
  }
]
```

Rank from best (1) to worst performance. Include reasoning for top 3 algorithms.
"""

        return prompt

    def add_feedback(self, prompt, prediction, feedback):
        """Add feedback for a prediction to improve future rankings."""
        self.context_examples.append((prompt, prediction, feedback))

        # Keep only recent examples to manage context size
        if len(self.context_examples) > self.max_context_examples:
            self.context_examples = self.context_examples[-self.max_context_examples:]

    def simulate_feedback(self, prediction):
        """
        Simulate feedback based on prediction quality.
        In a real system, this would come from business outcomes.
        """
        try:
            # Simple feedback simulation based on data quality
            if "json" in prediction.lower() and len(prediction) > 100:
                return "Good ranking structure provided with clear reasoning."
            else:
                return "Ranking needs more detail and better structure."
        except:
            return "Prediction format needs improvement."

# === API Caller ===
def call_minerva_icrl(system_prompt, user_prompt):
    payload = {
        "system_prompt": system_prompt,
        "user_prompt": user_prompt,
        "num_variations": 1,
        "temperature": 0.7,  # Higher temperature for more diverse learning
        "max_tokens": 2048,
        "model": "openai/gpt-3.5-turbo",
        "json_mode": False
    }
    response = requests.post(API_URL, headers=HEADERS, json=payload)
    response.raise_for_status()
    return response.json()

# === Main ICRL Execution ===
def main():
    dfs = load_all_campaigns(FOLDER_PATH)

    # Initialize ICRL ranker
    icrl_ranker = ICRLAlgorithmRanker()

    print("Starting ICRL Algorithm Ranking System")
    print("=" * 60)

    for fname, df in dfs.items():
        print(f"\n ICRL Analysis for: {fname}")
        print("="*80)

        try:
            # Parse campaign data
            parsed_df = icrl_ranker.parse_campaign_data(df)

            # Build system prompt
            system_prompt = icrl_ranker.build_icrl_system_prompt()

            # Build user prompt with context
            user_prompt = icrl_ranker.build_algorithm_ranking_prompt(parsed_df)

            # Make prediction
            result = call_minerva_icrl(system_prompt, user_prompt)

            if 'data' in result and len(result['data']) > 0:
                prediction = result['data'][0]

                print("\n ALGORITHM RANKING PREDICTION:")
                print("-" * 50)
                print(prediction)

                # Simulate feedback (in real system, this would be actual business feedback)
                feedback = icrl_ranker.simulate_feedback(prediction)

                print(f"\n FEEDBACK: {feedback}")

                # Add to context for future learning
                icrl_ranker.add_feedback(
                    prompt=f"Algorithm data for {fname}",
                    prediction=prediction,
                    feedback=feedback
                )

                print(f"\n Context Examples: {len(icrl_ranker.context_examples)}")

            else:
                print(" No valid response received")

        except Exception as e:
            print(f" Error processing {fname}: {e}")
            print(f"Error details: {str(e)}")

    print(f"\n🎓 ICRL Learning Complete!")
    print(f"Total context examples learned: {len(icrl_ranker.context_examples)}")

if __name__ == "__main__":
    main()
